{"name": "my-web", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fullcalendar/bootstrap5": "^6.1.15", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@fullcalendar/vue3": "^6.1.15", "axios": "^1.7.7", "bootstrap": "^5.3.3", "bootstrap-icons": "^1.11.3", "chart.js": "^4.4.5", "core-js": "^3.8.3", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.8.3", "fullcalendar": "^6.1.15", "vue": "^3.2.13", "vue-axios": "^3.5.2", "vue-chartjs": "^5.3.1", "vue-fullcalendar": "^1.0.9", "vue-router": "^4.4.5"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}