window.__VUE_PROD_HYDRATION_MISMATCH_DETAILS__ = false;
import { createApp } from 'vue'
import App from './App.vue'
const app = createApp(App)

// 导入Element Plus组件库
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
// 遍历ElementPlusIconsVue对象，将所有的图标组件注册到全局
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    // 向应用实例中注册全局组件
    app.component(key, component)
}

// 应用Element Plus组件库
app.use(ElementPlus,{size:'small',zIndex:3000})


// 导入路由
import router from './router'
// 全局配置路由
app.config.globalProperties.$router = router
// 使用路由
app.use(router)


// 添加axios插件
import axios from 'axios'
import VueAxios from 'vue-axios'
// 对axios对象进行全局注册，保证在任何组件内都可以使用
app.config.globalProperties.$axios = axios;
app.use(VueAxios, axios)


// 导入Chart.js的构造函数和其他注册项
import { Chart, registerables } from 'chart.js';
// 注册Chart.js组件
Chart.register(...registerables);
// 全局注册Chart.js组件
app.config.globalProperties.$Chart = Chart;







// 挂载
app.mount('#app')