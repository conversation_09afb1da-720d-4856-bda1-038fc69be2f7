<template>
  <div id="MyPage">
    <h1>MyPage</h1>
    <p>MyPageコンポーネントです。</p>
  </div>
</template>
<script>
export default {
  name: 'MyPage',
}
</script>

<style>
#MyPage {
    width: 100%;
    height: calc(100vh - 120px);
    min-height: 500px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    box-sizing: border-box;
    padding: 20px;
}

/* 滚动条样式 */
#MyPage::-webkit-scrollbar {
    width: 6px;
}

#MyPage::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

#MyPage::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

#MyPage::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}
</style>