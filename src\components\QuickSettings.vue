<template>
  <div class="quick-settings">
    <div class="settings-header">
      <h3 class="settings-title">
        <el-icon class="title-icon"><Setting /></el-icon>
        快速设置
      </h3>
    </div>

    <div class="settings-list">
      <!-- 主题切换 -->
      <div class="setting-item">
        <div class="setting-info">
          <div class="setting-icon" style="background-color: #409eff;">
            <el-icon><Sunny /></el-icon>
          </div>
          <div class="setting-content">
            <div class="setting-label">主题模式</div>
            <div class="setting-desc">切换浅色/深色主题</div>
          </div>
        </div>
        <el-switch 
          v-model="isDarkMode"
          @change="toggleTheme"
          active-color="#409eff"
          inactive-color="#dcdfe6"
        />
      </div>

      <!-- 通知设置 -->
      <div class="setting-item">
        <div class="setting-info">
          <div class="setting-icon" style="background-color: #67c23a;">
            <el-icon><Bell /></el-icon>
          </div>
          <div class="setting-content">
            <div class="setting-label">桌面通知</div>
            <div class="setting-desc">接收系统通知提醒</div>
          </div>
        </div>
        <el-switch 
          v-model="notificationEnabled"
          @change="toggleNotification"
          active-color="#67c23a"
          inactive-color="#dcdfe6"
        />
      </div>

      <!-- 自动保存 -->
      <div class="setting-item">
        <div class="setting-info">
          <div class="setting-icon" style="background-color: #e6a23c;">
            <el-icon><DocumentCopy /></el-icon>
          </div>
          <div class="setting-content">
            <div class="setting-label">自动保存</div>
            <div class="setting-desc">自动保存表单数据</div>
          </div>
        </div>
        <el-switch 
          v-model="autoSaveEnabled"
          @change="toggleAutoSave"
          active-color="#e6a23c"
          inactive-color="#dcdfe6"
        />
      </div>

      <!-- 语言设置 -->
      <div class="setting-item">
        <div class="setting-info">
          <div class="setting-icon" style="background-color: #f56c6c;">
            <el-icon><ChatDotRound /></el-icon>
          </div>
          <div class="setting-content">
            <div class="setting-label">语言</div>
            <div class="setting-desc">{{ currentLanguageLabel }}</div>
          </div>
        </div>
        <el-select 
          v-model="currentLanguage"
          @change="changeLanguage"
          size="small"
          style="width: 100px;"
        >
          <el-option 
            v-for="lang in languages" 
            :key="lang.value"
            :label="lang.label"
            :value="lang.value"
          />
        </el-select>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <h4 class="actions-title">快捷操作</h4>
      <div class="actions-grid">
        <el-button 
          class="action-btn"
          @click="exportData"
          icon="Download"
          size="small"
          plain
        >
          导出数据
        </el-button>
        <el-button 
          class="action-btn"
          @click="clearCache"
          icon="Delete"
          size="small"
          plain
        >
          清除缓存
        </el-button>
        <el-button 
          class="action-btn"
          @click="resetSettings"
          icon="RefreshRight"
          size="small"
          plain
        >
          重置设置
        </el-button>
        <el-button 
          class="action-btn"
          @click="contactSupport"
          icon="Service"
          size="small"
          plain
        >
          联系客服
        </el-button>
      </div>
    </div>

    <!-- 系统信息 -->
    <div class="system-info">
      <h4 class="info-title">系统信息</h4>
      <div class="info-list">
        <div class="info-item">
          <span class="info-label">版本号</span>
          <span class="info-value">v1.0.0</span>
        </div>
        <div class="info-item">
          <span class="info-label">浏览器</span>
          <span class="info-value">{{ browserInfo }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">分辨率</span>
          <span class="info-value">{{ screenResolution }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">在线状态</span>
          <span class="info-value" :class="{ 'online': isOnline, 'offline': !isOnline }">
            {{ isOnline ? '在线' : '离线' }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuickSettings',
  data() {
    return {
      isDarkMode: localStorage.getItem('theme') === 'dark',
      notificationEnabled: localStorage.getItem('desktopNotification') === 'true',
      autoSaveEnabled: localStorage.getItem('autoSave') !== 'false',
      currentLanguage: localStorage.getItem('language') || 'zh-CN',
      languages: [
        { label: '中文', value: 'zh-CN' },
        { label: 'English', value: 'en-US' },
        { label: '日本語', value: 'ja-JP' }
      ],
      isOnline: navigator.onLine
    }
  },
  computed: {
    currentLanguageLabel() {
      const lang = this.languages.find(l => l.value === this.currentLanguage)
      return lang ? lang.label : '中文'
    },
    browserInfo() {
      const ua = navigator.userAgent
      if (ua.includes('Chrome')) return 'Chrome'
      if (ua.includes('Firefox')) return 'Firefox'
      if (ua.includes('Safari')) return 'Safari'
      if (ua.includes('Edge')) return 'Edge'
      return 'Unknown'
    },
    screenResolution() {
      return `${screen.width}x${screen.height}`
    }
  },
  methods: {
    toggleTheme() {
      const theme = this.isDarkMode ? 'dark' : 'light'
      localStorage.setItem('theme', theme)
      this.$message.success(`已切换到${this.isDarkMode ? '深色' : '浅色'}主题`)
      
      // 这里可以添加实际的主题切换逻辑
      document.documentElement.setAttribute('data-theme', theme)
    },

    toggleNotification() {
      localStorage.setItem('desktopNotification', this.notificationEnabled)
      
      if (this.notificationEnabled) {
        // 请求通知权限
        if ('Notification' in window) {
          Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
              this.$message.success('桌面通知已开启')
            } else {
              this.notificationEnabled = false
              this.$message.warning('请在浏览器设置中允许通知权限')
            }
          })
        }
      } else {
        this.$message.success('桌面通知已关闭')
      }
    },

    toggleAutoSave() {
      localStorage.setItem('autoSave', this.autoSaveEnabled)
      this.$message.success(`自动保存已${this.autoSaveEnabled ? '开启' : '关闭'}`)
    },

    changeLanguage() {
      localStorage.setItem('language', this.currentLanguage)
      this.$message.success(`语言已切换为${this.currentLanguageLabel}`)
    },

    exportData() {
      // 模拟数据导出
      const data = {
        userInfo: {
          nickname: localStorage.getItem('nickname'),
          email: localStorage.getItem('nickname'),
          phone: localStorage.getItem('userPhone'),
          bio: localStorage.getItem('userBio')
        },
        settings: {
          theme: localStorage.getItem('theme'),
          language: localStorage.getItem('language'),
          notifications: localStorage.getItem('desktopNotification')
        },
        exportTime: new Date().toISOString()
      }

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `user-data-${new Date().toISOString().split('T')[0]}.json`
      a.click()
      URL.revokeObjectURL(url)

      this.$message.success('数据导出成功')
    },

    clearCache() {
      this.$confirm('确定要清除所有缓存数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除部分缓存，保留重要数据
        const importantKeys = ['nickname', 'userAvatar', 'registerTime']
        const allKeys = Object.keys(localStorage)
        
        allKeys.forEach(key => {
          if (!importantKeys.includes(key)) {
            localStorage.removeItem(key)
          }
        })

        this.$message.success('缓存清除成功')
      }).catch(() => {
        // 取消操作
      })
    },

    resetSettings() {
      this.$confirm('确定要重置所有设置为默认值吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 重置设置
        this.isDarkMode = false
        this.notificationEnabled = false
        this.autoSaveEnabled = true
        this.currentLanguage = 'zh-CN'

        localStorage.setItem('theme', 'light')
        localStorage.setItem('desktopNotification', 'false')
        localStorage.setItem('autoSave', 'true')
        localStorage.setItem('language', 'zh-CN')

        this.$message.success('设置已重置为默认值')
      }).catch(() => {
        // 取消操作
      })
    },

    contactSupport() {
      this.$message.info('客服功能开发中，请发送邮件至 <EMAIL>')
    },

    updateOnlineStatus() {
      this.isOnline = navigator.onLine
    }
  },

  mounted() {
    // 监听网络状态变化
    window.addEventListener('online', this.updateOnlineStatus)
    window.addEventListener('offline', this.updateOnlineStatus)
  },

  beforeUnmount() {
    window.removeEventListener('online', this.updateOnlineStatus)
    window.removeEventListener('offline', this.updateOnlineStatus)
  }
}
</script>

<style scoped>
.quick-settings {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  height: fit-content;
}

.settings-header {
  margin-bottom: 20px;
}

.settings-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.title-icon {
  color: #409eff;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 25px;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.setting-item:hover {
  background: rgba(255, 255, 255, 0.8);
}

.setting-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.setting-icon {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.setting-content {
  flex: 1;
}

.setting-label {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  line-height: 1.2;
}

.setting-desc {
  font-size: 12px;
  color: #7f8c8d;
  margin-top: 2px;
}

.quick-actions {
  margin-bottom: 25px;
}

.actions-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.action-btn {
  width: 100%;
  font-size: 12px;
}

.system-info {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 20px;
}

.info-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.info-label {
  color: #7f8c8d;
}

.info-value {
  color: #2c3e50;
  font-weight: 500;
}

.info-value.online {
  color: #67c23a;
}

.info-value.offline {
  color: #f56c6c;
}

@media (max-width: 768px) {
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .setting-item {
    padding: 12px;
  }
  
  .setting-icon {
    width: 32px;
    height: 32px;
  }
}
</style>
