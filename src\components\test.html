<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>柱状图示例</title>
    <style>
        #chart {
            width: 600px;
            height: 400px;
            border: 1px solid #ccc;
        }
    </style>
</head>
<body>
    <canvas id="chart"></canvas>
    <script>
        async function fetchData() {
            try {
                const response = await fetch('http://localhost:7777/esQuarterParkInfo/infrastructureDataByParkId', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ parkId: 20 })
                });
                const result = await response.json();
                if (result.success && result.data && result.data.records.length > 0) {
                    return result.data.records[0].基础设施.生态环境监控分析.土壤质量评估柱状图;
                }
                return [];
            } catch (error) {
                console.error('数据获取失败:', error);
                return [];
            }
        }

        async function drawChart() {
            const data = await fetchData();
            const labels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
            
            const canvas = document.getElementById("chart");
            const ctx = canvas.getContext("2d");
            canvas.width = 600;
            canvas.height = 400;

            const maxVal = Math.max(...data, 1);
            const barWidth = 40;
            const barSpacing = 20;
            const startX = 50;
            const startY = 350;

            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.font = "14px Arial";
            ctx.fillStyle = "black";

            for (let i = 0; i < data.length; i++) {
                let barHeight = (data[i] / maxVal) * 300;
                let x = startX + i * (barWidth + barSpacing);
                let y = startY - barHeight;
                
                ctx.fillStyle = "blue";
                ctx.fillRect(x, y, barWidth, barHeight);
                
                ctx.fillStyle = "black";
                ctx.fillText(labels[i], x + 5, startY + 20);
                ctx.fillText(data[i], x + 10, y - 5);
            }
        }

        drawChart();
    </script>
</body>
</html>