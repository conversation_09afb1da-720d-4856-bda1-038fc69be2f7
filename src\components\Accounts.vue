<template>
  <div class="accounts">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon class="title-icon"><CreditCard /></el-icon>
        资金账户管理
      </h2>
      <p class="page-subtitle">管理您的银行卡、电子钱包等资金存储位置</p>
    </div>

    <!-- 总览统计 -->
    <div class="overview-section">
      <div class="overview-grid">
        <div class="overview-card total">
          <div class="card-header">
            <div class="card-icon total-icon">
              <el-icon><Wallet /></el-icon>
            </div>
            <div class="card-badge">总资产</div>
          </div>
          <div class="card-content">
            <div class="amount-display">
              <span class="currency">¥</span>
              <span class="amount">{{ totalAssets.toLocaleString() }}</span>
            </div>
            <div class="change-info">
              <span class="change positive">
                <el-icon><TrendCharts /></el-icon>
                +{{ assetChange }}%
              </span>
              <span class="change-label">较上月</span>
            </div>
          </div>
        </div>

        <div class="overview-card accounts">
          <div class="card-header">
            <div class="card-icon accounts-icon">
              <el-icon><CreditCard /></el-icon>
            </div>
            <div class="card-badge">账户数量</div>
          </div>
          <div class="card-content">
            <div class="amount-display">
              <span class="amount">{{ accountList.length }}</span>
              <span class="unit">个</span>
            </div>
            <div class="change-info">
              <span class="change active">
                <el-icon><Check /></el-icon>
                {{ activeAccounts }}个活跃
              </span>
            </div>
          </div>
        </div>

        <div class="overview-card income">
          <div class="card-header">
            <div class="card-icon income-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="card-badge">本月收入</div>
          </div>
          <div class="card-content">
            <div class="amount-display">
              <span class="currency">¥</span>
              <span class="amount">{{ monthlyIncome.toLocaleString() }}</span>
            </div>
            <div class="change-info">
              <span class="change positive">
                <el-icon><ArrowUp /></el-icon>
                +{{ incomeChange }}%
              </span>
              <span class="change-label">较上月</span>
            </div>
          </div>
        </div>

        <div class="overview-card expense">
          <div class="card-header">
            <div class="card-icon expense-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="card-badge">本月支出</div>
          </div>
          <div class="card-content">
            <div class="amount-display">
              <span class="currency">¥</span>
              <span class="amount">{{ monthlyExpense.toLocaleString() }}</span>
            </div>
            <div class="change-info">
              <span class="change negative">
                <el-icon><ArrowUp /></el-icon>
                +{{ expenseChange }}%
              </span>
              <span class="change-label">较上月</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="showAddDialog = true" icon="Plus">添加账户</el-button>
      <el-button type="info" @click="refreshAccounts" icon="Refresh">刷新余额</el-button>
      <el-button type="warning" @click="exportAccounts" icon="Download">导出数据</el-button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 账户列表 -->
      <div class="accounts-grid">
        <div
          v-for="account in accountList"
          :key="account.id"
          class="account-card"
          :class="[account.type, account.status]"
        >
        <div class="account-header">
          <div class="account-icon-wrapper">
            <div class="account-icon" :class="account.type">
              <el-icon v-if="account.type === 'bank'"><CreditCard /></el-icon>
              <el-icon v-else-if="account.type === 'alipay'"><Money /></el-icon>
              <el-icon v-else-if="account.type === 'wechat'"><ChatDotRound /></el-icon>
              <el-icon v-else><Wallet /></el-icon>
            </div>
            <div class="account-type-badge">{{ getAccountTypeLabel(account.type) }}</div>
          </div>
          <div class="account-status" :class="account.status">
            <el-icon v-if="account.status === 'active'"><Check /></el-icon>
            <el-icon v-else><Close /></el-icon>
            {{ account.status === 'active' ? '正常' : '停用' }}
          </div>
        </div>

        <div class="account-info">
          <h3 class="account-name">{{ account.name }}</h3>
          <p class="account-number">{{ account.number }}</p>
          <p class="account-bank" v-if="account.bank">{{ account.bank }}</p>
        </div>

        <div class="account-balance">
          <div class="balance-main">
            <span class="balance-label">账户余额</span>
            <div class="balance-amount">
              <span class="currency">¥</span>
              <span class="amount">{{ account.balance.toLocaleString() }}</span>
            </div>
          </div>
          <div class="balance-update">
            <el-icon><Clock /></el-icon>
            更新于 {{ formatDate(account.lastUpdate) }}
          </div>
        </div>

        <div class="account-stats">
          <div class="stats-grid">
            <div class="stat-item income">
              <div class="stat-icon">
                <el-icon><ArrowUp /></el-icon>
              </div>
              <div class="stat-content">
                <span class="stat-label">本月收入</span>
                <span class="stat-value">¥{{ account.monthlyIncome.toLocaleString() }}</span>
              </div>
            </div>
            <div class="stat-item expense">
              <div class="stat-icon">
                <el-icon><ArrowDown /></el-icon>
              </div>
              <div class="stat-content">
                <span class="stat-label">本月支出</span>
                <span class="stat-value">¥{{ account.monthlyExpense.toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="account-actions">
          <el-button type="primary" size="small" @click="editAccount(account)" icon="Edit" plain>
            编辑
          </el-button>
          <el-button type="info" size="small" @click="viewTransactions(account)" icon="View" plain>
            交易
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="deleteAccount(account)"
            icon="Delete"
            :disabled="account.balance > 0"
            plain
          >
            删除
          </el-button>
        </div>
        </div>
      </div>

      <!-- 快速记账组件 -->
      <div class="quick-record-sidebar">
        <div class="quick-record-card">
          <div class="quick-record-header">
            <h3 class="quick-record-title">
              <el-icon class="title-icon"><Money /></el-icon>
              快速记账
            </h3>
            <el-button type="primary" size="small" @click="toQuickRecord" icon="Plus">
              记一笔
            </el-button>
          </div>

          <div class="recent-transactions">
            <h4 class="section-title">最近记录</h4>
            <div class="transaction-list">
              <div
                v-for="transaction in recentTransactions"
                :key="transaction.id"
                class="transaction-item"
                :class="transaction.type"
              >
                <div class="transaction-icon">
                  <el-icon v-if="transaction.type === 'income'"><ArrowUp /></el-icon>
                  <el-icon v-else><ArrowDown /></el-icon>
                </div>
                <div class="transaction-info">
                  <div class="transaction-category">{{ transaction.category }}</div>
                  <div class="transaction-account">{{ transaction.account }}</div>
                </div>
                <div class="transaction-amount" :class="transaction.type">
                  {{ transaction.type === 'income' ? '+' : '-' }}¥{{ transaction.amount }}
                </div>
              </div>
            </div>
          </div>

          <div class="quick-actions-section">
            <h4 class="section-title">快捷操作</h4>
            <div class="quick-action-buttons">
              <el-button type="success" size="small" @click="quickIncome" icon="Plus" plain>
                收入
              </el-button>
              <el-button type="danger" size="small" @click="quickExpense" icon="Minus" plain>
                支出
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑账户对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingAccount ? '编辑账户' : '添加账户'"
      width="500px"
      @close="resetAccountForm"
    >
      <el-form
        :model="accountForm"
        :rules="accountRules"
        ref="accountFormRef"
        label-width="100px"
      >
        <el-form-item label="账户名称" prop="name">
          <el-input v-model="accountForm.name" placeholder="请输入账户名称" />
        </el-form-item>

        <el-form-item label="账户类型" prop="type">
          <el-select v-model="accountForm.type" placeholder="请选择账户类型" style="width: 100%">
            <el-option label="银行卡" value="bank" />
            <el-option label="支付宝" value="alipay" />
            <el-option label="微信" value="wechat" />
            <el-option label="现金" value="cash" />
            <el-option label="其他" value="other" />
          </el-select>
        </el-form-item>

        <el-form-item label="账户号码" prop="number">
          <el-input v-model="accountForm.number" placeholder="请输入账户号码或卡号" />
        </el-form-item>

        <el-form-item label="初始余额" prop="balance">
          <el-input
            v-model="accountForm.balance"
            placeholder="请输入初始余额"
            type="number"
            step="0.01"
            min="0"
          >
            <template #prefix>¥</template>
          </el-input>
        </el-form-item>

        <el-form-item label="开户银行" prop="bank" v-if="accountForm.type === 'bank'">
          <el-select v-model="accountForm.bank" placeholder="请选择开户银行" style="width: 100%">
            <el-option label="中国工商银行" value="工商银行" />
            <el-option label="中国建设银行" value="建设银行" />
            <el-option label="中国农业银行" value="农业银行" />
            <el-option label="中国银行" value="中国银行" />
            <el-option label="招商银行" value="招商银行" />
            <el-option label="交通银行" value="交通银行" />
            <el-option label="其他银行" value="其他银行" />
          </el-select>
        </el-form-item>

        <el-form-item label="账户状态" prop="status">
          <el-radio-group v-model="accountForm.status">
            <el-radio value="active">正常</el-radio>
            <el-radio value="inactive">停用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="accountForm.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="3"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveAccount" :loading="saving">
          {{ editingAccount ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 交易记录对话框 -->
    <el-dialog
      v-model="showTransactionDialog"
      title="账户交易记录"
      width="800px"
    >
      <div v-if="selectedAccount" class="transaction-content">
        <div class="transaction-header">
          <h3>{{ selectedAccount.name }} 交易记录</h3>
          <p>账户余额: ¥{{ selectedAccount.balance.toLocaleString() }}</p>
        </div>

        <el-table :data="accountTransactions" style="width: 100%">
          <el-table-column prop="date" label="日期" width="120">
            <template #default="scope">
              {{ formatDate(scope.row.date) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="type" label="类型" width="80">
            <template #default="scope">
              <el-tag :type="scope.row.type === 'income' ? 'success' : 'danger'" size="small">
                {{ scope.row.type === 'income' ? '收入' : '支出' }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="category" label="分类" width="100" />
          
          <el-table-column prop="amount" label="金额" width="120">
            <template #default="scope">
              <span :class="scope.row.type === 'income' ? 'amount-income' : 'amount-expense'">
                {{ scope.row.type === 'income' ? '+' : '-' }}¥{{ scope.row.amount.toLocaleString() }}
              </span>
            </template>
          </el-table-column>
          
          <el-table-column prop="remark" label="备注" min-width="150" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import dayjs from 'dayjs'
import {
  CreditCard,
  Wallet,
  Money,
  TrendCharts,
  ChatDotRound,
  Check,
  Close,
  ArrowUp,
  ArrowDown,
  Clock
} from '@element-plus/icons-vue'

export default {
  name: 'AccountsPage',
  components: {
    CreditCard,
    Wallet,
    Money,
    TrendCharts,
    ChatDotRound,
    Check,
    Close,
    ArrowUp,
    ArrowDown,
    Clock
  },
  setup() {
    const showAddDialog = ref(false)
    const showTransactionDialog = ref(false)
    const editingAccount = ref(null)
    const selectedAccount = ref(null)
    const saving = ref(false)
    const accountFormRef = ref(null)

    // 账户表单
    const accountForm = reactive({
      name: '',
      type: '',
      number: '',
      balance: '',
      bank: '',
      status: 'active',
      remark: ''
    })

    // 表单验证规则
    const accountRules = {
      name: [
        { required: true, message: '请输入账户名称', trigger: 'blur' }
      ],
      type: [
        { required: true, message: '请选择账户类型', trigger: 'change' }
      ],
      number: [
        { required: true, message: '请输入账户号码', trigger: 'blur' }
      ],
      balance: [
        { required: true, message: '请输入初始余额', trigger: 'blur' },
        { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' }
      ]
    }

    // 账户列表
    const accountList = reactive([
      {
        id: 1,
        name: '工商银行储蓄卡',
        type: 'bank',
        number: '**** **** **** 1234',
        bank: '工商银行',
        balance: 25600,
        status: 'active',
        monthlyIncome: 12000,
        monthlyExpense: 8600,
        lastUpdate: '2024-06-24 10:30:00',
        remark: '主要工资卡'
      },
      {
        id: 2,
        name: '建设银行信用卡',
        type: 'bank',
        number: '**** **** **** 5678',
        bank: '建设银行',
        balance: 18900,
        status: 'active',
        monthlyIncome: 0,
        monthlyExpense: 3200,
        lastUpdate: '2024-06-24 09:15:00',
        remark: '日常消费信用卡'
      },
      {
        id: 3,
        name: '支付宝',
        type: 'alipay',
        number: '138****8001',
        bank: '',
        balance: 3200,
        status: 'active',
        monthlyIncome: 1500,
        monthlyExpense: 2800,
        lastUpdate: '2024-06-24 11:45:00',
        remark: '日常小额支付'
      },
      {
        id: 4,
        name: '微信钱包',
        type: 'wechat',
        number: '138****8001',
        bank: '',
        balance: 1800,
        status: 'active',
        monthlyIncome: 800,
        monthlyExpense: 1200,
        lastUpdate: '2024-06-24 08:20:00',
        remark: '微信支付'
      },
      {
        id: 5,
        name: '现金',
        type: 'cash',
        number: '现金',
        bank: '',
        balance: 500,
        status: 'active',
        monthlyIncome: 200,
        monthlyExpense: 300,
        lastUpdate: '2024-06-23 20:00:00',
        remark: '日常现金'
      }
    ])

    // 模拟交易记录
    const accountTransactions = reactive([
      {
        id: 1,
        date: '2024-06-24 09:00:00',
        type: 'income',
        category: '工资',
        amount: 8500,
        remark: '6月工资'
      },
      {
        id: 2,
        date: '2024-06-23 12:30:00',
        type: 'expense',
        category: '餐饮',
        amount: 268,
        remark: '午餐'
      },
      {
        id: 3,
        date: '2024-06-22 18:45:00',
        type: 'expense',
        category: '交通',
        amount: 156,
        remark: '打车费'
      }
    ])

    // 快速记账最近记录
    const recentTransactions = reactive([
      {
        id: 1,
        type: 'expense',
        category: '餐饮',
        account: '支付宝',
        amount: 268
      },
      {
        id: 2,
        type: 'income',
        category: '工资',
        account: '工商银行',
        amount: 8500
      },
      {
        id: 3,
        type: 'expense',
        category: '交通',
        account: '微信',
        amount: 156
      }
    ])

    // 计算总资产
    const totalAssets = computed(() => {
      return accountList.reduce((sum, account) => sum + account.balance, 0)
    })

    // 计算活跃账户数
    const activeAccounts = computed(() => {
      return accountList.filter(account => account.status === 'active').length
    })

    // 计算本月收入
    const monthlyIncome = computed(() => {
      return accountList.reduce((sum, account) => sum + account.monthlyIncome, 0)
    })

    // 计算本月支出
    const monthlyExpense = computed(() => {
      return accountList.reduce((sum, account) => sum + account.monthlyExpense, 0)
    })

    // 模拟变化百分比
    const assetChange = ref(5.2)
    const incomeChange = ref(12.5)
    const expenseChange = ref(8.3)

    return {
      showAddDialog,
      showTransactionDialog,
      editingAccount,
      selectedAccount,
      saving,
      accountFormRef,
      accountForm,
      accountRules,
      accountList,
      accountTransactions,
      recentTransactions,
      totalAssets,
      activeAccounts,
      monthlyIncome,
      monthlyExpense,
      assetChange,
      incomeChange,
      expenseChange
    }
  },
  methods: {
    // 获取账户类型标签
    getAccountTypeLabel(type) {
      const typeMap = {
        'bank': '银行卡',
        'alipay': '支付宝',
        'wechat': '微信',
        'cash': '现金',
        'other': '其他'
      }
      return typeMap[type] || type
    },

    // 编辑账户
    editAccount(account) {
      this.editingAccount = account
      Object.assign(this.accountForm, {
        name: account.name,
        type: account.type,
        number: account.number,
        balance: account.balance.toString(),
        bank: account.bank,
        status: account.status,
        remark: account.remark
      })
      this.showAddDialog = true
    },

    // 查看交易记录
    viewTransactions(account) {
      this.selectedAccount = account
      this.showTransactionDialog = true
    },

    // 删除账户
    deleteAccount(account) {
      if (account.balance > 0) {
        this.$message.warning('账户余额不为零，无法删除')
        return
      }

      this.$confirm(`确定要删除账户 ${account.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.accountList.findIndex(a => a.id === account.id)
        if (index > -1) {
          this.accountList.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },

    // 保存账户
    async saveAccount() {
      try {
        const valid = await this.$refs.accountFormRef.validate()
        if (!valid) return

        this.saving = true

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        if (this.editingAccount) {
          // 更新账户
          Object.assign(this.editingAccount, {
            ...this.accountForm,
            balance: parseFloat(this.accountForm.balance)
          })
          this.$message.success('更新成功')
        } else {
          // 添加新账户
          const newAccount = {
            id: Date.now(),
            ...this.accountForm,
            balance: parseFloat(this.accountForm.balance),
            monthlyIncome: 0,
            monthlyExpense: 0,
            lastUpdate: dayjs().format('YYYY-MM-DD HH:mm:ss')
          }
          this.accountList.push(newAccount)
          this.$message.success('添加成功')
        }

        this.showAddDialog = false
        this.resetAccountForm()
      } catch (error) {
        this.$message.error('操作失败，请重试')
      } finally {
        this.saving = false
      }
    },

    // 重置账户表单
    resetAccountForm() {
      this.editingAccount = null
      Object.assign(this.accountForm, {
        name: '',
        type: '',
        number: '',
        balance: '',
        bank: '',
        status: 'active',
        remark: ''
      })
      this.$refs.accountFormRef?.clearValidate()
    },

    // 刷新账户余额
    refreshAccounts() {
      this.$message.info('刷新功能开发中...')
    },

    // 导出账户数据
    exportAccounts() {
      this.$message.info('导出功能开发中...')
    },

    // 格式化日期
    formatDate(dateString) {
      return dayjs(dateString).format('MM-DD HH:mm')
    },

    // 快速记账相关方法
    toQuickRecord() {
      this.$router.push('/admin/IncomeRecord')
    },

    quickIncome() {
      this.$router.push('/admin/IncomeRecord?type=income')
    },

    quickExpense() {
      this.$router.push('/admin/IncomeRecord?type=expense')
    }
  }
}
</script>

<style scoped>
.accounts {
  width: 100%;
  height: 100%;
  padding: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 120px);
  min-height: 500px;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 20px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 32px;
  color: #409eff;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 总览区域 */
.overview-section {
  margin-bottom: 20px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
}

.overview-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  border-radius: 16px;
  padding: 18px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.overview-card.total::before {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.overview-card.accounts::before {
  background: linear-gradient(90deg, #722ed1, #9254de);
}

.overview-card.income::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.overview-card.expense::before {
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
}

.overview-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.total-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.accounts-icon {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.income-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.expense-icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.card-badge {
  background: rgba(0, 0, 0, 0.05);
  color: #666;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.card-content {
  flex: 1;
}

.amount-display {
  display: flex;
  align-items: baseline;
  gap: 4px;
  margin-bottom: 8px;
}

.currency {
  font-size: 18px;
  color: #666;
  font-weight: 500;
}

.amount {
  font-size: 26px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.unit {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.change-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
}

.change.positive {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.change.negative {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

.change.active {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.change-label {
  font-size: 12px;
  color: #999;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
  margin-bottom: 20px;
}

/* 主要内容区域 */
.main-content {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 20px;
  flex: 1;
  overflow: hidden;
}

/* 账户网格 */
.accounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  overflow-y: auto;
  max-height: 100%;
  padding-right: 8px;
}

/* 账户卡片 */
.account-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.account-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.account-card.bank::before {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.account-card.alipay::before {
  background: linear-gradient(90deg, #1677ff, #69b1ff);
}

.account-card.wechat::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.account-card.cash::before {
  background: linear-gradient(90deg, #fa8c16, #ffa940);
}

.account-card.other::before {
  background: linear-gradient(90deg, #722ed1, #9254de);
}

.account-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.account-card.inactive {
  opacity: 0.7;
}

/* 账户头部 */
.account-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 18px;
}

.account-icon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.account-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.account-icon.bank {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.account-icon.alipay {
  background: linear-gradient(135deg, #1677ff, #69b1ff);
}

.account-icon.wechat {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.account-icon.cash {
  background: linear-gradient(135deg, #fa8c16, #ffa940);
}

.account-icon.other {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.account-type-badge {
  background: rgba(0, 0, 0, 0.05);
  color: #666;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  min-width: 56px;
}

.account-status {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.account-status.active {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.account-status.inactive {
  background: linear-gradient(135deg, #d9d9d9, #f0f0f0);
  color: #666;
}

/* 账户信息 */
.account-info {
  margin-bottom: 16px;
}

.account-name {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1.2;
}

.account-number {
  margin: 0 0 6px 0;
  font-size: 14px;
  color: #7f8c8d;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  letter-spacing: 0.5px;
}

.account-bank {
  margin: 0;
  font-size: 13px;
  color: #409eff;
  font-weight: 500;
}

/* 账户余额 */
.account-balance {
  margin-bottom: 18px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(255, 255, 255, 0.6));
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
}

.balance-main {
  text-align: center;
  margin-bottom: 8px;
}

.balance-label {
  margin: 0 0 6px 0;
  font-size: 13px;
  color: #7f8c8d;
  font-weight: 500;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  margin: 0;
}

.balance-amount .currency {
  font-size: 18px;
  color: #666;
  font-weight: 500;
}

.balance-amount .amount {
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
  line-height: 1;
}

.balance-update {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin: 0;
  font-size: 12px;
  color: #bdc3c7;
}

/* 账户统计 */
.account-stats {
  margin-bottom: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.stat-item.income .stat-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.stat-item.expense .stat-icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #7f8c8d;
  margin-bottom: 4px;
  font-weight: 500;
}

.stat-value {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1;
}

/* 账户操作 */
.account-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.account-actions .el-button {
  flex: 1;
  min-height: 36px;
  border-radius: 10px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.account-actions .el-button:hover {
  transform: translateY(-1px);
}

/* 交易记录 */
.transaction-content {
  padding: 10px 0;
}

.transaction-header {
  margin-bottom: 20px;
  text-align: center;
}

.transaction-header h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.transaction-header p {
  margin: 0;
  font-size: 16px;
  color: #409eff;
  font-weight: 500;
}

/* 表格样式 */
.amount-income {
  color: #52c41a;
  font-weight: 600;
}

.amount-expense {
  color: #ff4d4f;
  font-weight: 600;
}

/* 快速记账侧边栏 */
.quick-record-sidebar {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.quick-record-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.4);
  height: fit-content;
  max-height: 500px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.quick-record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.quick-record-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.quick-record-title .title-icon {
  color: #409eff;
  font-size: 20px;
}

.recent-transactions {
  margin-bottom: 20px;
  flex: 0 0 auto;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 12px 0;
}

.transaction-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  overflow-y: auto;
  max-height: 180px;
  padding-right: 4px;
}

.transaction-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
}

.transaction-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.transaction-icon {
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  flex-shrink: 0;
}

.transaction-item.income .transaction-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.transaction-item.expense .transaction-icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.transaction-info {
  flex: 1;
  min-width: 0;
}

.transaction-category {
  font-size: 13px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 4px;
}

.transaction-account {
  font-size: 11px;
  color: #7f8c8d;
}

.transaction-amount {
  font-size: 14px;
  font-weight: 600;
  flex-shrink: 0;
}

.transaction-amount.income {
  color: #52c41a;
}

.transaction-amount.expense {
  color: #ff4d4f;
}

.quick-actions-section {
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding-top: 15px;
}

.quick-action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.quick-action-buttons .el-button {
  border-radius: 10px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .main-content {
    grid-template-columns: 1fr 280px;
    gap: 15px;
  }

  .accounts-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
  }

  .quick-record-card {
    padding: 16px;
  }
}

@media (max-width: 768px) {
  .accounts {
    padding: 12px;
  }

  .page-title {
    font-size: 24px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .main-content {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .accounts-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .account-card {
    padding: 16px;
  }

  .action-bar {
    flex-direction: column;
    gap: 8px;
  }

  .action-bar .el-button {
    width: 100%;
  }

  .quick-record-sidebar {
    order: -1;
  }

  .quick-record-card {
    padding: 15px;
  }

  .transaction-list {
    max-height: 200px;
  }
}

@media (max-width: 480px) {
  .accounts {
    padding: 8px;
  }

  .page-title {
    font-size: 20px;
  }

  .overview-card {
    padding: 12px;
    gap: 8px;
  }

  .card-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .card-content .amount {
    font-size: 18px;
  }

  .account-card {
    padding: 12px;
  }

  .account-actions {
    flex-direction: column;
  }

  .account-actions .el-button {
    max-width: none;
  }
}

/* Element Plus 组件自定义样式 */
:deep(.el-dialog) {
  border-radius: 16px;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-radio-button__inner) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}

:deep(.el-table) {
  background: transparent;
}

:deep(.el-table th) {
  background: rgba(255, 255, 255, 0.6);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

:deep(.el-table td) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

:deep(.el-table tr:hover > td) {
  background: rgba(255, 255, 255, 0.8);
}

:deep(.el-tag) {
  border-radius: 6px;
}

/* 滚动条样式优化 */
.accounts::-webkit-scrollbar,
.accounts-grid::-webkit-scrollbar {
  width: 6px;
}

.accounts::-webkit-scrollbar-track,
.accounts-grid::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.accounts::-webkit-scrollbar-thumb,
.accounts-grid::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.accounts::-webkit-scrollbar-thumb:hover,
.accounts-grid::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style>
